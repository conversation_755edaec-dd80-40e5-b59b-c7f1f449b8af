  Application android.app  Build android.app.Activity  Bundle android.app.Activity  RequiresApi android.app.Activity  Context android.content  Build android.content.Context  Bundle android.content.Context  RequiresApi android.content.Context  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  RequiresApi android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  
VERSION_CODES android.os.Build  O android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  RequiresApi  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  RequiresApi androidx.annotation  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  Preferences #androidx.datastore.preferences.core  	ViewModel androidx.lifecycle  Boolean androidx.lifecycle.ViewModel  CompletionRepository androidx.lifecycle.ViewModel  CreateHabitUiState androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  HabitFrequency androidx.lifecycle.ViewModel  HabitRepository androidx.lifecycle.ViewModel  HabitSection androidx.lifecycle.ViewModel  HabitSectionRepository androidx.lifecycle.ViewModel  HabitWithCompletions androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  ManageSectionsUiState androidx.lifecycle.ViewModel  Map androidx.lifecycle.ViewModel  
ReminderState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  UserPreferencesRepository androidx.lifecycle.ViewModel  WeekInfo androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  
ForeignKey 
androidx.room  Index 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  CASCADE androidx.room.ForeignKey  CASCADE "androidx.room.ForeignKey.Companion  invoke "androidx.room.ForeignKey.Companion  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  
CompletionDao androidx.room.RoomDatabase  HabitDao androidx.room.RoomDatabase  HabitSectionDao androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  
trimIndent !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  HabitsApplication com.example.habits9  Boolean com.example.habits9.data  
Completion com.example.habits9.data  
CompletionDao com.example.habits9.data  CompletionRepository com.example.habits9.data  Dao com.example.habits9.data  Delete com.example.habits9.data  Double com.example.habits9.data  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  	HabitType com.example.habits9.data  Insert com.example.habits9.data  Int com.example.habits9.data  List com.example.habits9.data  Long com.example.habits9.data  NumericalHabitType com.example.habits9.data  OnConflictStrategy com.example.habits9.data  Query com.example.habits9.data  String com.example.habits9.data  Update com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  	dataStore com.example.habits9.data  Long #com.example.habits9.data.Completion  
PrimaryKey #com.example.habits9.data.Completion  String #com.example.habits9.data.Completion  
Completion &com.example.habits9.data.CompletionDao  Delete &com.example.habits9.data.CompletionDao  Flow &com.example.habits9.data.CompletionDao  Insert &com.example.habits9.data.CompletionDao  List &com.example.habits9.data.CompletionDao  Long &com.example.habits9.data.CompletionDao  OnConflictStrategy &com.example.habits9.data.CompletionDao  Query &com.example.habits9.data.CompletionDao  Update &com.example.habits9.data.CompletionDao  
Completion -com.example.habits9.data.CompletionRepository  
CompletionDao -com.example.habits9.data.CompletionRepository  Flow -com.example.habits9.data.CompletionRepository  Inject -com.example.habits9.data.CompletionRepository  List -com.example.habits9.data.CompletionRepository  Long -com.example.habits9.data.CompletionRepository  Boolean com.example.habits9.data.Habit  Double com.example.habits9.data.Habit  	HabitType com.example.habits9.data.Habit  Int com.example.habits9.data.Habit  Long com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  
PrimaryKey com.example.habits9.data.Habit  String com.example.habits9.data.Habit  Delete !com.example.habits9.data.HabitDao  Flow !com.example.habits9.data.HabitDao  Habit !com.example.habits9.data.HabitDao  Insert !com.example.habits9.data.HabitDao  List !com.example.habits9.data.HabitDao  Long !com.example.habits9.data.HabitDao  OnConflictStrategy !com.example.habits9.data.HabitDao  Query !com.example.habits9.data.HabitDao  Update !com.example.habits9.data.HabitDao  
CompletionDao &com.example.habits9.data.HabitDatabase  HabitDao &com.example.habits9.data.HabitDatabase  HabitSectionDao &com.example.habits9.data.HabitDatabase  Flow (com.example.habits9.data.HabitRepository  Habit (com.example.habits9.data.HabitRepository  HabitDao (com.example.habits9.data.HabitRepository  Inject (com.example.habits9.data.HabitRepository  List (com.example.habits9.data.HabitRepository  Long (com.example.habits9.data.HabitRepository  Int %com.example.habits9.data.HabitSection  
PrimaryKey %com.example.habits9.data.HabitSection  String %com.example.habits9.data.HabitSection  Delete (com.example.habits9.data.HabitSectionDao  Flow (com.example.habits9.data.HabitSectionDao  HabitSection (com.example.habits9.data.HabitSectionDao  Insert (com.example.habits9.data.HabitSectionDao  List (com.example.habits9.data.HabitSectionDao  OnConflictStrategy (com.example.habits9.data.HabitSectionDao  Query (com.example.habits9.data.HabitSectionDao  Update (com.example.habits9.data.HabitSectionDao  Flow /com.example.habits9.data.HabitSectionRepository  HabitSection /com.example.habits9.data.HabitSectionRepository  HabitSectionDao /com.example.habits9.data.HabitSectionRepository  Inject /com.example.habits9.data.HabitSectionRepository  List /com.example.habits9.data.HabitSectionRepository  ApplicationContext 2com.example.habits9.data.UserPreferencesRepository  Context 2com.example.habits9.data.UserPreferencesRepository  Flow 2com.example.habits9.data.UserPreferencesRepository  Inject 2com.example.habits9.data.UserPreferencesRepository  String 2com.example.habits9.data.UserPreferencesRepository  DatabaseModule com.example.habits9.di  SingletonComponent com.example.habits9.di  
trimIndent com.example.habits9.di  ApplicationContext %com.example.habits9.di.DatabaseModule  
CompletionDao %com.example.habits9.di.DatabaseModule  CompletionRepository %com.example.habits9.di.DatabaseModule  Context %com.example.habits9.di.DatabaseModule  HabitDao %com.example.habits9.di.DatabaseModule  
HabitDatabase %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionDao %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  	Migration %com.example.habits9.di.DatabaseModule  Provides %com.example.habits9.di.DatabaseModule  	Singleton %com.example.habits9.di.DatabaseModule  SupportSQLiteDatabase %com.example.habits9.di.DatabaseModule  
getTRIMIndent %com.example.habits9.di.DatabaseModule  
getTrimIndent %com.example.habits9.di.DatabaseModule  
trimIndent %com.example.habits9.di.DatabaseModule  
getTRIMIndent Fcom.example.habits9.di.DatabaseModule.MIGRATION_3_4.<no name provided>  
getTrimIndent Fcom.example.habits9.di.DatabaseModule.MIGRATION_3_4.<no name provided>  
getTRIMIndent Fcom.example.habits9.di.DatabaseModule.MIGRATION_4_5.<no name provided>  
getTrimIndent Fcom.example.habits9.di.DatabaseModule.MIGRATION_4_5.<no name provided>  Boolean com.example.habits9.ui  Float com.example.habits9.ui  HabitWithCompletions com.example.habits9.ui  Int com.example.habits9.ui  List com.example.habits9.ui  Long com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  Map com.example.habits9.ui  String com.example.habits9.ui  WeekInfo com.example.habits9.ui  com com.example.habits9.ui  Boolean $com.example.habits9.ui.MainViewModel  CompletionRepository $com.example.habits9.ui.MainViewModel  Float $com.example.habits9.ui.MainViewModel  HabitRepository $com.example.habits9.ui.MainViewModel  HabitWithCompletions $com.example.habits9.ui.MainViewModel  Inject $com.example.habits9.ui.MainViewModel  Int $com.example.habits9.ui.MainViewModel  List $com.example.habits9.ui.MainViewModel  Long $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  Map $com.example.habits9.ui.MainViewModel  	StateFlow $com.example.habits9.ui.MainViewModel  String $com.example.habits9.ui.MainViewModel  WeekInfo $com.example.habits9.ui.MainViewModel  com $com.example.habits9.ui.MainViewModel  CreateHabitUiState "com.example.habits9.ui.createhabit  CreateHabitViewModel "com.example.habits9.ui.createhabit  HabitFrequency "com.example.habits9.ui.createhabit  Int "com.example.habits9.ui.createhabit  
ReminderState "com.example.habits9.ui.createhabit  String "com.example.habits9.ui.createhabit  Unit "com.example.habits9.ui.createhabit  com "com.example.habits9.ui.createhabit  CreateHabitUiState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitFrequency 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitSection 7com.example.habits9.ui.createhabit.CreateHabitViewModel  HabitSectionRepository 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Inject 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Int 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
ReminderState 7com.example.habits9.ui.createhabit.CreateHabitViewModel  	StateFlow 7com.example.habits9.ui.createhabit.CreateHabitViewModel  String 7com.example.habits9.ui.createhabit.CreateHabitViewModel  Unit 7com.example.habits9.ui.createhabit.CreateHabitViewModel  com 7com.example.habits9.ui.createhabit.CreateHabitViewModel  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  
AccentPrimary com.example.habits9.ui.details  DarkBackground com.example.habits9.ui.details  DividerColor com.example.habits9.ui.details  SurfaceVariantDark com.example.habits9.ui.details  TextPrimary com.example.habits9.ui.details  
TextSecondary com.example.habits9.ui.details  
AccentPrimary )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  BackgroundDark com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  
AccentPrimary %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  HabitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  Inject =com.example.habits9.ui.managesections.ManageSectionsViewModel  Int =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  	StateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  String =com.example.habits9.ui.managesections.ManageSectionsViewModel  
AccentPrimary com.example.habits9.ui.settings  DarkBackground com.example.habits9.ui.settings  SettingsViewModel com.example.habits9.ui.settings  String com.example.habits9.ui.settings  SurfaceVariantDark com.example.habits9.ui.settings  TextPrimary com.example.habits9.ui.settings  
TextSecondary com.example.habits9.ui.settings  Inject 1com.example.habits9.ui.settings.SettingsViewModel  	StateFlow 1com.example.habits9.ui.settings.SettingsViewModel  String 1com.example.habits9.ui.settings.SettingsViewModel  UserPreferencesRepository 1com.example.habits9.ui.settings.SettingsViewModel  Build com.example.uhabits_99  MainActivity com.example.uhabits_99  Build #com.example.uhabits_99.MainActivity  Bundle #com.example.uhabits_99.MainActivity  RequiresApi #com.example.uhabits_99.MainActivity  DarkColorScheme com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  HiltViewModelMap &dagger.hilt.android.internal.lifecycle  KeySet 7dagger.hilt.android.internal.lifecycle.HiltViewModelMap  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  OriginatingElement dagger.hilt.codegen  SingletonComponent dagger.hilt.components  GeneratedEntryPoint dagger.hilt.internal  IntoMap dagger.multibindings  IntoSet dagger.multibindings  	StringKey dagger.multibindings  Build 	java.lang  
Completion 	java.lang  Habit 	java.lang  HabitSection 	java.lang  OnConflictStrategy 	java.lang  SingletonComponent 	java.lang  String 	java.lang  com 	java.lang  
trimIndent 	java.lang  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Array kotlin  Boolean kotlin  Build kotlin  
Completion kotlin  Double kotlin  Float kotlin  Habit kotlin  HabitSection kotlin  Int kotlin  Long kotlin  OnConflictStrategy kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  arrayOf kotlin  com kotlin  
trimIndent kotlin  
getTRIMIndent 
kotlin.String  
getTrimIndent 
kotlin.String  Build kotlin.annotation  
Completion kotlin.annotation  Habit kotlin.annotation  HabitSection kotlin.annotation  OnConflictStrategy kotlin.annotation  SingletonComponent kotlin.annotation  com kotlin.annotation  
trimIndent kotlin.annotation  Build kotlin.collections  
Completion kotlin.collections  Habit kotlin.collections  HabitSection kotlin.collections  List kotlin.collections  Map kotlin.collections  OnConflictStrategy kotlin.collections  SingletonComponent kotlin.collections  com kotlin.collections  
trimIndent kotlin.collections  Build kotlin.comparisons  
Completion kotlin.comparisons  Habit kotlin.comparisons  HabitSection kotlin.comparisons  OnConflictStrategy kotlin.comparisons  SingletonComponent kotlin.comparisons  com kotlin.comparisons  
trimIndent kotlin.comparisons  Build 	kotlin.io  
Completion 	kotlin.io  Habit 	kotlin.io  HabitSection 	kotlin.io  OnConflictStrategy 	kotlin.io  SingletonComponent 	kotlin.io  com 	kotlin.io  
trimIndent 	kotlin.io  Build 
kotlin.jvm  
Completion 
kotlin.jvm  Habit 
kotlin.jvm  HabitSection 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  SingletonComponent 
kotlin.jvm  com 
kotlin.jvm  
trimIndent 
kotlin.jvm  Build 
kotlin.ranges  
Completion 
kotlin.ranges  Habit 
kotlin.ranges  HabitSection 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  SingletonComponent 
kotlin.ranges  com 
kotlin.ranges  
trimIndent 
kotlin.ranges  KClass kotlin.reflect  Build kotlin.sequences  
Completion kotlin.sequences  Habit kotlin.sequences  HabitSection kotlin.sequences  OnConflictStrategy kotlin.sequences  SingletonComponent kotlin.sequences  com kotlin.sequences  
trimIndent kotlin.sequences  Build kotlin.text  
Completion kotlin.text  Habit kotlin.text  HabitSection kotlin.text  OnConflictStrategy kotlin.text  SingletonComponent kotlin.text  com kotlin.text  
trimIndent kotlin.text  Flow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  Double androidx.lifecycle.ViewModel  Double com.example.habits9.ui  Double $com.example.habits9.ui.MainViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 