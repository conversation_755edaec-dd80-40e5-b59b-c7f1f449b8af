## Objective: Implement New UI for Measurable Habit Tracking

The goal is to enhance the user interface for measurable habits to provide better clarity and context. This involves creating a new input dialog and redesigning how progress is displayed on the main screen.

## Visual References
* **Old Input Dialog:** `23.jpg` (Shows the simple dialog we are replacing).
* **New Input Dialog:** `24.jpg` (This is the design we will now implement).
* **Old Main Screen:** `23.jpg` (Shows values inside circles, which we will change).

## Implementation Plan

### 1. Update the Main Habit List UI

This is the most critical part of the change. We will display the progress and target as text next to the habit name.

* **File to Modify:** The Kotlin file and XML layout responsible for a single row in the main habit list (`RecyclerView` item).
* **Action:**
    1.  In the XML layout for the habit row, add a new `TextView` next to the `TextView` that displays the habit's name. This new view will display the progress text (e.g., "3.0 / at least 5 km").
    2.  In the `onBindViewHolder` (or equivalent `bind` function) of your `RecyclerView.Adapter`, add the following logic:
        * **If the habit's type is "Yes/No,"** hide the new progress `TextView`.
        * **If the habit's type is "Measurable,"** make the progress `TextView` visible and populate it with the correct string.
            * Fetch the current day's logged `value` for the habit.
            * Fetch the habit's `target` value and `targetType` (e.g., "at least").
            * Format the string as: **"[Logged Value] / [Target Type] [Target Value] [Units]"** (e.g., "3.0 / at least 5 km").
            * If no value is logged for the current day, you can display something like "0 / at least 5 km".

### 2. Implement the New Input Dialog

* **Goal:** Create the new input dialog exactly as shown in `24.jpg`.
* **Action:**
    1.  Create a new XML layout for the `AlertDialog`. This layout should include:
        * A `TextView` for the habit name.
        * A `TextView` for the date.
        * A `TextView` to display the formatted target (e.g., "Target: at least 3 km").
        * An `EditText` that only accepts numerical/decimal input.
        * "Cancel" and "OK" buttons.
    2.  Modify the logic that launches the dialog (likely in your `HabitListView.kt` or its controlling Activity/Fragment).
    3.  When creating the dialog, pass all the necessary data (habit name, date, target information) to populate the new `TextViews` you created.

### 3. Refine the Logic for the Completion Circle

The circle's role will now be a simple visual indicator.

* **File to Modify:** The `onBindViewHolder` (or `bind` function) in your adapter.
* **Action:**
    * For **Yes/No habits**, the circle's checked state is based on whether it is marked complete.
    * For **Measurable habits**, the circle's checked state should now be based on whether the logged `value` meets the `target`.
        * If `targetType` is "at least" and `value >= target`, the circle is checked.
        * If `targetType` is "at most" and `value <= target`, the circle is checked.
        * Otherwise, it is unchecked.

### 4. Verification

Thoroughly test the new implementation:
1.  **Main Screen:**
    * Confirm that the progress text appears correctly for measurable habits and is hidden for Yes/No habits.
    * Confirm the circle's state accurately reflects whether the target has been met.
2.  **Input Dialog:**
    * Confirm the new dialog appears for measurable habits and displays all information correctly.
    * Log a new value and confirm the main screen's progress text and circle update instantly and correctly.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.