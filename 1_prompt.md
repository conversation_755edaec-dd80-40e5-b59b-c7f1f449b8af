## Objective: Fix UI Bugs and Refine Measurable Habit UX

The goal is to fix three bugs related to the new measurable habits feature: the main screen UI does not update correctly, the completion percentage is calculated incorrectly, and the input dialog does not match the required design.

## Visual References
* `24.jpg`: The **target design** for the numerical input dialog.
* `25.jpg`: Demonstrates the bug where the progress text does not update and the completion percentage is wrong.
* `26.jpg`: Shows the **current incorrect design** of the input dialog that needs to be replaced.

## Bug Descriptions and Implementation Plan

### 1. Fix Stale Progress Text on the Main Screen

* **Problem:** As seen in `25.jpg`, after a user logs a value for a measurable habit (e.g., "1 km"), the new value appears in the circle but the text next to the habit name is not updated to reflect this new value. It remains unchanged until a full screen refresh.
* **Root Cause:** The UI update logic is not correctly triggering a re-render of the progress text after the value is saved.
* **Implementation:**
    1.  In the `ViewModel` or `Presenter`, locate the function that saves the measurable habit's value.
    2.  Immediately after the database update is successful, ensure that the logic which refreshes the habit list's data is called.
    3.  This refresh must provide the updated `Habit` object (with its new completion data) to the `RecyclerView.Adapter` so that the progress text can be re-rendered with the latest value.

### 2. Correct "Today's Completion" Percentage Logic

* **Problem:** As shown in `25.jpg`, "Today's Completion" percentage is 100% even though a measurable habit is only partially complete (e.g., 1km of a 3km target).
* **Root Cause:** The calculation logic is counting any non-zero entry for a measurable habit as a full completion.
* **Implementation:**
    1.  Locate the function responsible for calculating the "Today's Completion" percentage.
    2.  Modify the logic to correctly evaluate measurable habits. A measurable habit should only be counted as "complete" for that day if its logged `value` meets its `target` (e.g., `value >= target` for "at least" habits, or `value <= target` for "at most" habits).
    3.  Habits that have a logged value but do not meet the target should not be counted towards the completed percentage.

### 3. Redesign the Numerical Input Dialog

* **Problem:** The current input dialog, shown in `26.jpg`, is too simple and does not provide enough context to the user.
* **Requirement:** The dialog must be redesigned to match the layout and information shown in `24.jpg`.
* **Implementation:**
    1.  Create a new XML layout for an `AlertDialog` that contains the following elements:
        * `TextView` for the habit name.
        * `TextView` for the date.
        * `TextView` for the target (e.g., "Target: at least 3 km").
        * `EditText` for numerical input.
        * "Cancel" and "OK" buttons.
    2.  Replace the old dialog creation logic with this new layout.
    3.  Ensure that all the data (habit name, date, target info) is correctly passed to and displayed in the dialog when it opens.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.