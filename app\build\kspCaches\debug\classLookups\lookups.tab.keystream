  Activity android.app  Context android.content  ContextWrapper android.content  Build 
android.os  
VERSION_CODES android.os.Build  ContextThemeWrapper android.view  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  
ForeignKey 
androidx.room  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion androidx.room.ForeignKey  	Companion  androidx.room.OnConflictStrategy  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  
Completion com.example.habits9.data  
CompletionDao com.example.habits9.data  CompletionRepository com.example.habits9.data  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  UserPreferencesRepository com.example.habits9.data  DatabaseModule com.example.habits9.di  <no name provided> 3com.example.habits9.di.DatabaseModule.MIGRATION_3_4  <no name provided> 3com.example.habits9.di.DatabaseModule.MIGRATION_4_5  
MainViewModel com.example.habits9.ui  CreateHabitViewModel "com.example.habits9.ui.createhabit  ManageSectionsViewModel %com.example.habits9.ui.managesections  SettingsViewModel com.example.habits9.ui.settings  MainActivity com.example.uhabits_99  String kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    